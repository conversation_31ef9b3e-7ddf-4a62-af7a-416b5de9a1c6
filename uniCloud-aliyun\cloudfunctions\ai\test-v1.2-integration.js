// V1.2 集成测试脚本
// 测试完整的智能执行流程：意图识别 -> 智能计划生成 -> 动态参数解析 -> 真实工具调用

// 导入主文件中的类和函数
const fs = require('fs')
const path = require('path')

// 读取主文件内容并执行（模拟云函数环境）
const mainFileContent = fs.readFileSync(path.join(__dirname, 'index.obj.js'), 'utf8')

// 模拟 uniCloud 环境
global.uniCloud = {
  callFunction: async (options) => {
    console.log(`模拟调用云函数: ${options.name}`)
    console.log('参数:', JSON.stringify(options.data, null, 2))
    
    // 模拟不同工具的返回结果
    if (options.name === 'dida-todo' && options.data.action === 'getProjects') {
      return {
        result: {
          success: true,
          data: [
            { id: 'proj-1', name: 'OKR项目', description: '目标管理项目' },
            { id: 'proj-2', name: '日常任务', description: '日常工作任务' },
            { id: 'proj-3', name: 'OKR管理', description: 'OKR系统管理' }
          ]
        }
      }
    }
    
    if (options.name === 'dida-todo' && options.data.action === 'getTasks') {
      return {
        result: {
          success: true,
          tasks: [
            { id: 'task-1', title: '制定Q1目标', completed: false, projectId: options.data.projectId },
            { id: 'task-2', title: '更新KR进度', completed: true, projectId: options.data.projectId },
            { id: 'task-3', title: '团队会议', completed: false, projectId: options.data.projectId }
          ]
        }
      }
    }
    
    return { result: { success: false, error: '未知操作' } }
  },
  
  serializeSSEChannel: () => 'mock-channel',
  deserializeSSEChannel: () => ({
    write: async (data) => {
      console.log('SSE消息:', JSON.stringify(data, null, 2))
    },
    end: async (data) => {
      console.log('SSE结束:', JSON.stringify(data, null, 2))
    }
  })
}

// 模拟 OpenAI
global.OpenAI = class {
  constructor(config) {
    this.config = config
  }
  
  get chat() {
    return {
      completions: {
        create: async (options) => {
          console.log('模拟AI调用:', options.messages[1].content.substring(0, 100) + '...')
          
          // 模拟AI生成的执行计划
          const mockPlan = {
            analysis: "用户想要查看OKR项目的任务",
            steps: [
              {
                toolName: "getProjects",
                description: "获取项目列表，筛选OKR相关项目",
                parameters: { filter: "okr" },
                dependencies: [],
                reasoning: "首先需要找到OKR相关的项目"
              },
              {
                toolName: "getTasks", 
                description: "获取目标项目下的任务",
                parameters: {
                  projectId: "$context.targetProject.id",
                  completed: false
                },
                dependencies: ["step1"],
                reasoning: "基于找到的项目获取其任务列表"
              }
            ]
          }
          
          return {
            choices: [{
              message: {
                content: JSON.stringify(mockPlan)
              }
            }]
          }
        }
      }
    }
  }
}

// 执行主文件代码（去掉 module.exports 部分）
const codeToExecute = mainFileContent.replace(/module\.exports\s*=[\s\S]*$/, '')

// 创建一个新的上下文来执行代码
const vm = require('vm')
const context = vm.createContext({
  ...global,
  console,
  require,
  __dirname,
  __filename,
  module: { exports: {} },
  exports: {}
})

vm.runInContext(codeToExecute, context)

// 导出需要的类和函数
const {
  IntelligentExecutionPlanner,
  DynamicParameterResolver,
  ExecutionContextManager,
  executeIntelligentPlan,
  TOOL_REGISTRY,
  ParameterValidator
} = context

// 集成测试函数
async function testCompleteWorkflow() {
  console.log('=== V1.2 完整工作流程测试 ===\n')
  
  try {
    // 1. 测试智能执行计划生成
    console.log('1. 测试智能执行计划生成...')
    const plan = await IntelligentExecutionPlanner.generatePlan('查看okr项目的任务', 'find_task')
    
    console.log('生成的执行计划:')
    console.log('- 计划ID:', plan.planId)
    console.log('- 总步骤数:', plan.totalSteps)
    console.log('- 预计总时间:', plan.estimatedTotalTime + 'ms')
    
    plan.steps.forEach((step, index) => {
      console.log(`- 步骤${index + 1}: ${step.description}`)
      console.log(`  工具: ${step.toolName}`)
      console.log(`  参数: ${JSON.stringify(step.parameters)}`)
      console.log(`  依赖: [${step.dependencies.join(', ')}]`)
    })
    
    // 2. 测试执行上下文管理
    console.log('\n2. 测试执行上下文管理...')
    const context = new ExecutionContextManager('test-session', '查看okr项目的任务')
    
    // 3. 测试完整执行流程
    console.log('\n3. 测试完整执行流程...')
    
    // 模拟SSE通道
    const mockSSEChannel = {
      write: async (data) => {
        console.log(`[SSE] ${data.type}:`, JSON.stringify(data, null, 2))
      },
      end: async (data) => {
        console.log(`[SSE] 结束:`, JSON.stringify(data, null, 2))
      }
    }
    
    // 执行智能计划
    await executeIntelligentPlan(plan, context, mockSSEChannel)
    
    // 4. 验证执行结果
    console.log('\n4. 验证执行结果...')
    console.log('上下文数据:')
    for (const [key, value] of context.contextData.entries()) {
      console.log(`- ${key}:`, value)
    }
    
    console.log('\n步骤结果:')
    for (const [stepId, stepData] of context.stepResults.entries()) {
      console.log(`- ${stepId}:`, stepData.result?.success ? '成功' : '失败')
    }
    
    console.log('\n✅ 完整工作流程测试成功！')
    
  } catch (error) {
    console.error('\n❌ 集成测试失败:', error.message)
    console.error(error.stack)
  }
}

// 测试动态参数解析的复杂场景
async function testComplexParameterResolution() {
  console.log('\n=== 复杂动态参数解析测试 ===\n')
  
  try {
    const context = new ExecutionContextManager('test-session', '查找工作类型的OKR项目')
    
    // 设置复杂的步骤结果
    context.setStepResult('step1', {
      success: true,
      data: [
        { id: 'proj-1', name: 'OKR项目', type: 'work', priority: 'high' },
        { id: 'proj-2', name: '日常任务', type: 'personal', priority: 'medium' },
        { id: 'proj-3', name: 'OKR管理', type: 'work', priority: 'high' },
        { id: 'proj-4', name: '学习计划', type: 'personal', priority: 'low' }
      ]
    })
    
    // 测试复杂的筛选表达式
    const complexStep = {
      parameters: {
        // 筛选工作类型的项目
        workProjects: '$filter(step1.data, type equals "work")',
        // 筛选高优先级项目
        highPriorityProjects: '$filter(step1.data, priority equals "high")',
        // 筛选包含OKR的项目
        okrProjects: '$filter(step1.data, name contains "okr")',
        // 获取第一个工作项目的ID
        firstWorkProjectId: '$step.step1.data[type=work].id'
      },
      dependencies: ['step1']
    }
    
    console.log('解析复杂动态参数...')
    const resolved = await DynamicParameterResolver.resolveParameters(complexStep, context)
    
    console.log('解析结果:')
    console.log('- 工作项目数量:', resolved.workProjects.length)
    console.log('- 高优先级项目数量:', resolved.highPriorityProjects.length) 
    console.log('- OKR项目数量:', resolved.okrProjects.length)
    console.log('- 第一个工作项目ID:', resolved.firstWorkProjectId)
    
    // 验证结果
    const workProjectsCorrect = resolved.workProjects.length === 2
    const highPriorityCorrect = resolved.highPriorityProjects.length === 2
    const okrProjectsCorrect = resolved.okrProjects.length === 2
    const firstWorkProjectCorrect = resolved.firstWorkProjectId === 'proj-1'
    
    console.log('\n验证结果:')
    console.log('✅ 工作项目筛选:', workProjectsCorrect)
    console.log('✅ 高优先级筛选:', highPriorityCorrect)
    console.log('✅ OKR项目筛选:', okrProjectsCorrect)
    console.log('✅ 数组筛选路径:', firstWorkProjectCorrect)
    
    if (workProjectsCorrect && highPriorityCorrect && okrProjectsCorrect && firstWorkProjectCorrect) {
      console.log('\n✅ 复杂参数解析测试成功！')
    } else {
      console.log('\n❌ 复杂参数解析测试失败！')
    }
    
  } catch (error) {
    console.error('\n❌ 复杂参数解析测试失败:', error.message)
    console.error(error.stack)
  }
}

// 运行所有集成测试
async function runIntegrationTests() {
  console.log('开始 V1.2 集成测试...\n')
  
  try {
    await testCompleteWorkflow()
    await testComplexParameterResolution()
    
    console.log('\n🎉 所有集成测试完成！')
    
  } catch (error) {
    console.error('\n💥 集成测试失败:', error.message)
    console.error(error.stack)
  }
}

// 执行测试
if (require.main === module) {
  runIntegrationTests()
}
