// V1.2 功能测试脚本
// 测试动态参数解析器、智能执行计划生成器和真实工具调用引擎

// 简化的测试类定义
class ExecutionContextManager {
  constructor(sessionId, userInput) {
    this.sessionId = sessionId
    this.userInput = userInput
    this.stepResults = new Map()
    this.contextData = new Map()
    this.metadata = {
      startTime: Date.now(),
      currentStep: 0,
      totalSteps: 0
    }
  }

  setStepResult(stepId, result, metadata = {}) {
    this.stepResults.set(stepId, {
      result,
      metadata: {
        ...metadata,
        timestamp: Date.now(),
        stepIndex: this.metadata.currentStep
      }
    })
    this.extractContextData(stepId, result)
  }

  getStepResult(stepId) {
    return this.stepResults.get(stepId)?.result
  }

  setContextData(key, value) {
    this.contextData.set(key, value)
  }

  getContextData(key) {
    return this.contextData.get(key)
  }

  extractContextData(stepId, result) {
    if (result.data && Array.isArray(result.data)) {
      const targetProject = this.findTargetProject(result.data)
      if (targetProject) {
        this.setContextData('targetProject', targetProject)
      }
    }
    
    if (result.tasks && Array.isArray(result.tasks)) {
      this.setContextData('taskCount', result.tasks.length)
      this.setContextData('uncompletedTasks', result.tasks.filter(t => !t.completed))
    }
  }

  findTargetProject(projects) {
    const userInput = this.userInput.toLowerCase()
    const keywords = this.extractKeywords(userInput)
    
    const scored = projects.map(project => ({
      project,
      score: this.calculateMatchScore(project, keywords)
    }))
    
    scored.sort((a, b) => b.score - a.score)
    return scored.length > 0 && scored[0].score > 0 ? scored[0].project : null
  }

  extractKeywords(input) {
    const keywords = []
    const projectMatches = input.match(/(\w+)项目|(\w+)project/gi)
    if (projectMatches) {
      projectMatches.forEach(match => {
        const keyword = match.replace(/项目|project/gi, '')
        if (keyword) keywords.push(keyword.toLowerCase())
      })
    }
    return keywords
  }

  calculateMatchScore(project, keywords) {
    let score = 0
    const projectName = project.name.toLowerCase()
    keywords.forEach(keyword => {
      if (projectName.includes(keyword)) {
        score += 1
      }
    })
    return score
  }
}

// 简化的动态参数解析器
class DynamicParameterResolver {
  static async resolveParameters(step, context) {
    const { parameters, dependencies } = step
    const resolved = { ...parameters }

    // 等待依赖步骤完成
    for (const depId of dependencies) {
      await this.waitForStepCompletion(depId, context)
    }

    // 解析动态参数
    for (const [key, value] of Object.entries(resolved)) {
      if (typeof value === 'string') {
        resolved[key] = await this.resolveDynamicValue(value, context)
      }
    }

    return resolved
  }

  static async resolveDynamicValue(value, context) {
    // 处理上下文引用：$context.key
    if (value.startsWith('$context.')) {
      const contextPath = value.substring(9)
      const [contextKey, ...pathParts] = contextPath.split('.')
      const contextValue = context.getContextData(contextKey)
      if (contextValue !== undefined) {
        if (pathParts.length > 0) {
          return this.extractValueByPath(contextValue, pathParts.join('.'))
        }
        return contextValue
      }
      throw new Error(`上下文数据不存在: ${contextKey}`)
    }

    // 处理步骤结果引用：$step.stepId.path
    if (value.startsWith('$step.')) {
      const [, stepId, ...pathParts] = value.split('.')
      const stepResult = context.getStepResult(stepId)
      
      if (!stepResult) {
        throw new Error(`步骤结果不存在: ${stepId}`)
      }

      return this.extractValueByPath(stepResult, pathParts.join('.'))
    }

    // 处理筛选表达式：$filter(stepId.path, condition)
    if (value.startsWith('$filter(')) {
      return this.processFilterExpression(value, context)
    }

    return value
  }

  static extractValueByPath(obj, path) {
    if (!path) return obj
    
    return path.split('.').reduce((current, key) => {
      // 处理数组索引：projects[0]
      const arrayMatch = key.match(/^(\w+)\[(\d+)\]$/)
      if (arrayMatch) {
        const [, arrayKey, index] = arrayMatch
        return current?.[arrayKey]?.[parseInt(index)]
      }
      
      // 处理数组筛选：projects[name=okr]
      const filterMatch = key.match(/^(\w+)\[(\w+)=(.+)\]$/)
      if (filterMatch) {
        const [, arrayKey, filterKey, filterValue] = filterMatch
        const array = current?.[arrayKey]
        if (Array.isArray(array)) {
          return array.find(item => 
            item[filterKey]?.toLowerCase().includes(filterValue.toLowerCase())
          )
        }
      }
      
      return current?.[key]
    }, obj)
  }

  static async processFilterExpression(expression, context) {
    // 解析筛选表达式：$filter(step1.projects, name contains "okr")
    const match = expression.match(/\$filter\(([^,]+),\s*(.+)\)/)
    if (!match) {
      throw new Error(`无效的筛选表达式: ${expression}`)
    }

    const [, dataPath, condition] = match

    // 处理步骤结果引用
    if (dataPath.startsWith('step')) {
      const [stepId, ...pathParts] = dataPath.split('.')
      const stepResult = context.getStepResult(stepId)

      if (!stepResult) {
        throw new Error(`步骤结果不存在: ${stepId}`)
      }

      const data = this.extractValueByPath(stepResult, pathParts.join('.'))

      if (!Array.isArray(data)) {
        throw new Error(`筛选目标必须是数组: ${dataPath}`)
      }

      return this.applyFilter(data, condition)
    }

    // 其他情况使用通用解析
    const data = await this.resolveDynamicValue(`$${dataPath}`, context)

    if (!Array.isArray(data)) {
      throw new Error(`筛选目标必须是数组: ${dataPath}`)
    }

    return this.applyFilter(data, condition)
  }

  static applyFilter(array, condition) {
    // 解析条件：name contains "okr"
    const conditionMatch = condition.match(/(\w+)\s+(contains|equals|startsWith)\s+"([^"]+)"/)
    if (!conditionMatch) {
      throw new Error(`无效的筛选条件: ${condition}`)
    }

    const [, field, operator, value] = conditionMatch
    
    return array.filter(item => {
      const fieldValue = item[field]?.toString().toLowerCase() || ''
      const searchValue = value.toLowerCase()
      
      switch (operator) {
        case 'contains':
          return fieldValue.includes(searchValue)
        case 'equals':
          return fieldValue === searchValue
        case 'startsWith':
          return fieldValue.startsWith(searchValue)
        default:
          return false
      }
    })
  }

  static async waitForStepCompletion(stepId, context) {
    // 等待依赖步骤完成的逻辑
    return new Promise((resolve) => {
      const checkCompletion = () => {
        const stepResult = context.getStepResult(stepId)
        if (stepResult !== undefined) {
          resolve()
        } else {
          setTimeout(checkCompletion, 100)
        }
      }
      checkCompletion()
    })
  }
}

// 测试用例 1: 动态参数解析测试
async function testDynamicParameterResolver() {
  console.log('=== 测试动态参数解析器 ===')
  
  const context = new ExecutionContextManager('session-1', '查看okr项目任务')
  
  // 设置上下文数据
  context.setContextData('targetProject', { id: 'proj-1', name: 'OKR项目' })
  
  // 设置步骤结果
  context.setStepResult('step1', {
    projects: [
      { id: 'proj-1', name: 'OKR项目' },
      { id: 'proj-2', name: '日常任务' }
    ]
  })
  
  // 测试上下文引用
  const step1 = {
    parameters: { projectId: '$context.targetProject.id', completed: false },
    dependencies: []
  }
  
  const resolved1 = await DynamicParameterResolver.resolveParameters(step1, context)
  console.log('上下文引用解析结果:', resolved1)
  console.log('✅ projectId 解析正确:', resolved1.projectId === 'proj-1')
  
  // 测试步骤结果引用
  const step2 = {
    parameters: { projectName: '$step.step1.projects[0].name' },
    dependencies: []
  }
  
  const resolved2 = await DynamicParameterResolver.resolveParameters(step2, context)
  console.log('步骤结果引用解析结果:', resolved2)
  console.log('✅ projectName 解析正确:', resolved2.projectName === 'OKR项目')
}

// 测试用例 2: 筛选表达式测试
async function testFilterExpression() {
  console.log('\n=== 测试筛选表达式 ===')
  
  const context = new ExecutionContextManager('session-1', 'test')
  context.setStepResult('step1', {
    projects: [
      { id: 'proj-1', name: 'OKR项目', type: 'work' },
      { id: 'proj-2', name: '日常任务', type: 'personal' },
      { id: 'proj-3', name: 'OKR管理', type: 'work' }
    ]
  })
  
  // 测试 contains 筛选
  const result1 = await DynamicParameterResolver.processFilterExpression(
    '$filter(step1.projects, name contains "okr")', 
    context
  )
  console.log('contains 筛选结果:', result1.map(p => p.name))
  console.log('✅ contains 筛选正确:', result1.length === 2)
  
  // 测试 equals 筛选
  const result2 = await DynamicParameterResolver.processFilterExpression(
    '$filter(step1.projects, type equals "work")', 
    context
  )
  console.log('equals 筛选结果:', result2.map(p => p.name))
  console.log('✅ equals 筛选正确:', result2.length === 2)
}

// 测试用例 3: 路径提取测试
function testPathExtraction() {
  console.log('\n=== 测试路径提取 ===')
  
  const testData = {
    projects: [
      { id: 'proj-1', name: 'OKR项目', tasks: [{ title: '任务1' }, { title: '任务2' }] },
      { id: 'proj-2', name: '日常任务', tasks: [{ title: '任务3' }] }
    ],
    user: { name: '张三', role: 'admin' }
  }
  
  // 测试简单路径
  const result1 = DynamicParameterResolver.extractValueByPath(testData, 'user.name')
  console.log('简单路径提取:', result1)
  console.log('✅ 简单路径正确:', result1 === '张三')
  
  // 测试数组索引
  const result2 = DynamicParameterResolver.extractValueByPath(testData, 'projects[0].name')
  console.log('数组索引提取:', result2)
  console.log('✅ 数组索引正确:', result2 === 'OKR项目')
  
  // 测试数组筛选
  const result3 = DynamicParameterResolver.extractValueByPath(testData, 'projects[name=okr].id')
  console.log('数组筛选提取:', result3)
  console.log('✅ 数组筛选正确:', result3 === 'proj-1')
}

// 运行所有测试
async function runAllTests() {
  console.log('开始 V1.2 功能测试...\n')
  
  try {
    await testDynamicParameterResolver()
    await testFilterExpression()
    testPathExtraction()
    
    console.log('\n✅ 所有测试完成！')
  } catch (error) {
    console.error('\n❌ 测试失败:', error.message)
    console.error(error.stack)
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllTests()
}

module.exports = {
  testDynamicParameterResolver,
  testFilterExpression,
  testPathExtraction,
  runAllTests
}
