// V1.2 集成测试脚本
// 测试完整的智能执行流程：意图识别 -> 智能计划生成 -> 动态参数解析 -> 真实工具调用

// 导入主文件中的类和函数
const fs = require('fs')
const path = require('path')

// 读取主文件内容并执行（模拟云函数环境）
const mainFileContent = fs.readFileSync(path.join(__dirname, 'index.obj.js'), 'utf8')

// 模拟 uniCloud 环境
global.uniCloud = {
  callFunction: async (options) => {
    console.log(`模拟调用云函数：${options.name}`)
    console.log('参数：', JSON.stringify(options.data, null, 2))
    
    // 模拟不同工具的返回结果
    if (options.name === 'dida-todo' && options.data.action === 'getProjects') {
      return {
        result: {
          success: true,
          data: [
            { id: 'proj-1', name: 'OKR 项目', description: '目标管理项目' },
            { id: 'proj-2', name: '日常任务', description: '日常工作任务' },
            { id: 'proj-3', name: 'OKR 管理', description: 'OKR 系统管理' }
          ]
        }
      }
    }
    
    if (options.name === 'dida-todo' && options.data.action === 'getTasks') {
      return {
        result: {
          success: true,
          tasks: [
            { id: 'task-1', title: '制定 Q1 目标', completed: false, projectId: options.data.projectId },
            { id: 'task-2', title: '更新 KR 进度', completed: true, projectId: options.data.projectId },
            { id: 'task-3', title: '团队会议', completed: false, projectId: options.data.projectId }
          ]
        }
      }
    }
    
    return { result: { success: false, error: '未知操作' } }
  },
  
  serializeSSEChannel: () => 'mock-channel',
  deserializeSSEChannel: () => ({
    write: async (data) => {
      console.log('SSE 消息：', JSON.stringify(data, null, 2))
    },
    end: async (data) => {
      console.log('SSE 结束：', JSON.stringify(data, null, 2))
    }
  })
}

// 模拟 OpenAI
global.OpenAI = class {
  constructor(config) {
    this.config = config
  }
  
  get chat() {
    return {
      completions: {
        create: async (options) => {
          console.log('模拟 AI 调用：', options.messages[1].content.substring(0, 100) + '...')
          
          // 模拟 AI 生成的执行计划
          const mockPlan = {
            analysis: "用户想要查看 OKR 项目的任务",
            steps: [
              {
                toolName: "getProjects",
                description: "获取项目列表，筛选 OKR 相关项目",
                parameters: { filter: "okr" },
                dependencies: [],
                reasoning: "首先需要找到 OKR 相关的项目"
              },
              {
                toolName: "getTasks", 
                description: "获取目标项目下的任务",
                parameters: {
                  projectId: "$context.targetProject.id",
                  completed: false
                },
                dependencies: ["step1"],
                reasoning: "基于找到的项目获取其任务列表"
              }
            ]
          }
          
          return {
            choices: [{
              message: {
                content: JSON.stringify(mockPlan)
              }
            }]
          }
        }
      }
    }
  }
}

// 执行主文件代码（去掉 module.exports 部分）
const codeToExecute = mainFileContent.replace(/module\.exports\s*=[\s\S]*$/, '')

// 创建一个新的上下文来执行代码
const vm = require('vm')
const context = vm.createContext({
  ...global,
  console,
  require,
  __dirname,
  __filename,
  module: { exports: {} },
  exports: {}
})

vm.runInContext(codeToExecute, context)

// 导出需要的类和函数
const {
  IntelligentExecutionPlanner,
  DynamicParameterResolver,
  ExecutionContextManager,
  executeIntelligentPlan,
  TOOL_REGISTRY,
  ParameterValidator
} = context

// 集成测试函数
async function testCompleteWorkflow() {
  console.log('=== V1.2 完整工作流程测试 ===\n')
  
  try {
    // 1. 测试智能执行计划生成
    console.log('1. 测试智能执行计划生成...')
    const plan = await IntelligentExecutionPlanner.generatePlan('查看 okr 项目的任务', 'find_task')
    
    console.log('生成的执行计划：')
    console.log('- 计划 ID:', plan.planId)
    console.log('- 总步骤数：', plan.totalSteps)
    console.log('- 预计总时间：', plan.estimatedTotalTime + 'ms')
    
    plan.steps.forEach((step, index) => {
      console.log(`- 步骤${index + 1}: ${step.description}`)
      console.log(`  工具：${step.toolName}`)
      console.log(`  参数：${JSON.stringify(step.parameters)}`)
      console.log(`  依赖：[${step.dependencies.join(', ')}]`)
    })
    
    // 2. 测试执行上下文管理
    console.log('\n2. 测试执行上下文管理...')
    const context = new ExecutionContextManager('test-session', '查看 okr 项目的任务')
    
    // 3. 测试完整执行流程
    console.log('\n3. 测试完整执行流程...')
    
    // 模拟 SSE 通道
    const mockSSEChannel = {
      write: async (data) => {
        console.log(`[SSE] ${data.type}:`, JSON.stringify(data, null, 2))
      },
      end: async (data) => {
        console.log(`[SSE] 结束:`, JSON.stringify(data, null, 2))
      }
    }
    
    // 执行智能计划
    await executeIntelligentPlan(plan, context, mockSSEChannel)
    
    // 4. 验证执行结果
    console.log('\n4. 验证执行结果...')
    console.log('上下文数据：')
    for (const [key, value] of context.contextData.entries()) {
      console.log(`- ${key}:`, value)
    }
    
    console.log('\n步骤结果：')
    for (const [stepId, stepData] of context.stepResults.entries()) {
      console.log(`- ${stepId}:`, stepData.result?.success ? '成功' : '失败')
    }
    
    console.log('\n✅ 完整工作流程测试成功！')
    
  } catch (error) {
    console.error('\n❌ 集成测试失败：', error.message)
    console.error(error.stack)
  }
}

// 测试动态参数解析的复杂场景
async function testComplexParameterResolution() {
  console.log('\n=== 复杂动态参数解析测试 ===\n')
  
  try {
    const context = new ExecutionContextManager('test-session', '查找工作类型的 OKR 项目')
    
    // 设置复杂的步骤结果
    context.setStepResult('step1', {
      success: true,
      data: [
        { id: 'proj-1', name: 'OKR 项目', type: 'work', priority: 'high' },
        { id: 'proj-2', name: '日常任务', type: 'personal', priority: 'medium' },
        { id: 'proj-3', name: 'OKR 管理', type: 'work', priority: 'high' },
        { id: 'proj-4', name: '学习计划', type: 'personal', priority: 'low' }
      ]
    })
    
    // 测试复杂的筛选表达式
    const complexStep = {
      parameters: {
        // 筛选工作类型的项目
        workProjects: '$filter(step1.data, type equals "work")',
        // 筛选高优先级项目
        highPriorityProjects: '$filter(step1.data, priority equals "high")',
        // 筛选包含 OKR 的项目
        okrProjects: '$filter(step1.data, name contains "okr")',
        // 获取第一个工作项目的 ID
        firstWorkProjectId: '$step.step1.data[type=work].id'
      },
      dependencies: ['step1']
    }
    
    console.log('解析复杂动态参数...')
    const resolved = await DynamicParameterResolver.resolveParameters(complexStep, context)
    
    console.log('解析结果：')
    console.log('- 工作项目数量：', resolved.workProjects.length)
    console.log('- 高优先级项目数量：', resolved.highPriorityProjects.length) 
    console.log('- OKR 项目数量：', resolved.okrProjects.length)
    console.log('- 第一个工作项目 ID:', resolved.firstWorkProjectId)
    
    // 验证结果
    const workProjectsCorrect = resolved.workProjects.length === 2
    const highPriorityCorrect = resolved.highPriorityProjects.length === 2
    const okrProjectsCorrect = resolved.okrProjects.length === 2
    const firstWorkProjectCorrect = resolved.firstWorkProjectId === 'proj-1'
    
    console.log('\n验证结果：')
    console.log('✅ 工作项目筛选：', workProjectsCorrect)
    console.log('✅ 高优先级筛选：', highPriorityCorrect)
    console.log('✅ OKR 项目筛选：', okrProjectsCorrect)
    console.log('✅ 数组筛选路径：', firstWorkProjectCorrect)
    
    if (workProjectsCorrect && highPriorityCorrect && okrProjectsCorrect && firstWorkProjectCorrect) {
      console.log('\n✅ 复杂参数解析测试成功！')
    } else {
      console.log('\n❌ 复杂参数解析测试失败！')
    }
    
  } catch (error) {
    console.error('\n❌ 复杂参数解析测试失败：', error.message)
    console.error(error.stack)
  }
}

// 运行所有集成测试
async function runIntegrationTests() {
  console.log('开始 V1.2 集成测试...\n')
  
  try {
    await testCompleteWorkflow()
    await testComplexParameterResolution()
    
    console.log('\n🎉 所有集成测试完成！')
    
  } catch (error) {
    console.error('\n💥 集成测试失败：', error.message)
    console.error(error.stack)
  }
}

// 执行测试
if (require.main === module) {
  runIntegrationTests()
}
      planId: this.generateUUID(),
      userInput: userInput,
      intentType: intentType,
      steps: [],
      totalSteps: 0,
      status: 'pending',
      startTime: Date.now()
    }

    switch (intentType) {
      case 'find_task':
        if (userInput.includes('项目') || userInput.includes('project')) {
          executionPlan.steps = [
            {
              stepId: this.generateUUID(),
              toolName: 'getProjects',
              description: '获取项目列表',
              parameters: { filter: this.extractProjectKeyword(userInput) },
              dependencies: [],
              status: 'pending'
            },
            {
              stepId: this.generateUUID(),
              toolName: 'getTasks',
              description: '获取项目下的任务',
              parameters: {
                projectId: '$context.targetProject.id',
                completed: false
              },
              dependencies: ['step1'],
              status: 'pending'
            }
          ]
        } else {
          executionPlan.steps = [
            {
              stepId: this.generateUUID(),
              toolName: 'getTasks',
              description: '获取任务列表',
              parameters: { completed: false },
              dependencies: [],
              status: 'pending'
            }
          ]
        }
        break
      case 'create_task':
        break
      default:
        break
    }

    executionPlan.totalSteps = executionPlan.steps.length
    return executionPlan
  }

  static extractProjectKeyword(input) {
    const matches = input.match(/(\w+)项目|(\w+)project/gi)
    if (matches && matches.length > 0) {
      return matches[0].replace(/项目|project/gi, '')
    }
    return ''
  }

  static generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c == 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }
}

// V1.2 智能执行计划生成器
class IntelligentExecutionPlanner {
  static async generatePlan(userInput, intentType) {
    const executionPlan = {
      planId: this.generateUUID(),
      userInput: userInput,
      intentType: intentType,
      steps: [],
      totalSteps: 0,
      status: 'pending',
      startTime: Date.now(),
      estimatedTotalTime: 0
    }

    try {
      const analysisPrompt = this.buildAnalysisPrompt(userInput, intentType)
      const aiResponse = await this.callAI(analysisPrompt)

      const planData = JSON.parse(aiResponse)

      for (let i = 0; i < planData.steps.length; i++) {
        const stepData = planData.steps[i]
        const toolConfig = TOOL_REGISTRY[stepData.toolName]

        const step = {
          stepId: this.generateUUID(),
          toolName: stepData.toolName,
          description: stepData.description,
          parameters: stepData.parameters,
          dependencies: stepData.dependencies || [],
          status: 'pending',
          retryCount: 0,
          maxRetries: 3,
          executionTime: null,
          estimatedTime: toolConfig?.metadata?.estimatedTime || 2000,
          error: null
        }

        executionPlan.steps.push(step)
        executionPlan.estimatedTotalTime += step.estimatedTime
      }

      executionPlan.totalSteps = executionPlan.steps.length
      return executionPlan

    } catch (error) {
      console.warn('AI执行计划解析失败，使用默认计划:', error)
      return this.generateDefaultPlan(userInput, intentType)
    }
  }

  static buildAnalysisPrompt(userInput, intentType) {
    const toolPrompt = this.generateToolPrompt(TOOL_REGISTRY)

    return `分析用户输入："${userInput}"
意图类型：${intentType}

${toolPrompt}

请生成执行计划，支持以下动态参数引用：
- $context.key: 引用上下文数据
- $step.stepId.path: 引用前置步骤的结果
- $filter(stepId.path, condition): 对数据进行筛选

返回JSON格式：
{
  "analysis": "用户意图分析",
  "steps": [
    {
      "toolName": "工具名称",
      "description": "步骤描述",
      "parameters": {
        "param1": "静态值",
        "param2": "$context.targetProject.id"
      },
      "dependencies": ["step1"],
      "reasoning": "选择此工具的原因"
    }
  ]
}`
  }

  static generateToolPrompt(toolRegistry) {
    let prompt = '可用工具列表：\n'
    for (const [toolName, config] of Object.entries(toolRegistry)) {
      prompt += `- ${toolName}: ${config.description}\n`
      prompt += `  参数: ${JSON.stringify(config.parameters)}\n`
    }
    return prompt
  }

  static async callAI(prompt) {
    const openai = new OpenAI({
      baseURL: 'https://ark.cn-beijing.volces.com/api/v3',
      apiKey: '***********************************'
    })

    const response = await openai.chat.completions.create({
      messages: [
        { role: 'system', content: '你是一个专业的任务执行计划生成器。' },
        { role: 'user', content: prompt }
      ],
      model: 'doubao-seed-1-6-250615',
      stream: false,
      timeout: 30000
    })

    return response.choices[0].message.content
  }

  static generateDefaultPlan(userInput, intentType) {
    const executionPlan = {
      planId: this.generateUUID(),
      userInput: userInput,
      intentType: intentType,
      steps: [],
      totalSteps: 0,
      status: 'pending',
      startTime: Date.now(),
      estimatedTotalTime: 0
    }

    if (intentType === 'find_task') {
      if (userInput.includes('项目') || userInput.includes('project')) {
        const step1Id = this.generateUUID()
        executionPlan.steps = [
          {
            stepId: step1Id,
            toolName: 'getProjects',
            description: '获取项目列表',
            parameters: { filter: this.extractProjectKeyword(userInput) },
            dependencies: [],
            status: 'pending',
            estimatedTime: 1500
          },
          {
            stepId: this.generateUUID(),
            toolName: 'getTasks',
            description: '获取项目下的任务',
            parameters: {
              projectId: '$context.targetProject.id',
              completed: false
            },
            dependencies: [step1Id],
            status: 'pending',
            estimatedTime: 2000
          }
        ]
        executionPlan.estimatedTotalTime = 3500
      } else {
        executionPlan.steps = [
          {
            stepId: this.generateUUID(),
            toolName: 'getTasks',
            description: '获取任务列表',
            parameters: { completed: false },
            dependencies: [],
            status: 'pending',
            estimatedTime: 2000
          }
        ]
        executionPlan.estimatedTotalTime = 2000
      }
    }

    executionPlan.totalSteps = executionPlan.steps.length
    return executionPlan
  }

  static extractProjectKeyword(input) {
    const matches = input.match(/(\w+)项目|(\w+)project/gi)
    if (matches && matches.length > 0) {
      return matches[0].replace(/项目|project/gi, '')
    }
    return ''
  }

  static generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0
      const v = c == 'x' ? r : (r & 0x3 | 0x8)
      return v.toString(16)
    })
  }
}

// V1.2 动态参数解析器
class DynamicParameterResolver {
  static async resolveParameters(step, context) {
    const { parameters, dependencies } = step
    const resolved = { ...parameters }

    // 等待依赖步骤完成
    for (const depId of dependencies) {
      await this.waitForStepCompletion(depId, context)
    }

    // 解析动态参数
    for (const [key, value] of Object.entries(resolved)) {
      if (typeof value === 'string') {
        resolved[key] = await this.resolveDynamicValue(value, context)
      }
    }

    return resolved
  }

  static async resolveDynamicValue(value, context) {
    // 处理上下文引用：$context.key
    if (value.startsWith('$context.')) {
      const contextPath = value.substring(9)
      const [contextKey, ...pathParts] = contextPath.split('.')
      const contextValue = context.getContextData(contextKey)
      if (contextValue !== undefined) {
        if (pathParts.length > 0) {
          return this.extractValueByPath(contextValue, pathParts.join('.'))
        }
        return contextValue
      }
      throw new Error(`上下文数据不存在：${contextKey}`)
    }

    // 处理步骤结果引用：$step.stepId.path
    if (value.startsWith('$step.')) {
      const [, stepId, ...pathParts] = value.split('.')
      const stepResult = context.getStepResult(stepId)

      if (!stepResult) {
        throw new Error(`步骤结果不存在：${stepId}`)
      }

      return this.extractValueByPath(stepResult, pathParts.join('.'))
    }

    // 处理筛选表达式：$filter(stepId.path, condition)
    if (value.startsWith('$filter(')) {
      return this.processFilterExpression(value, context)
    }

    return value
  }

  static extractValueByPath(obj, path) {
    if (!path) return obj

    return path.split('.').reduce((current, key) => {
      // 处理数组索引：projects[0]
      const arrayMatch = key.match(/^(\w+)\[(\d+)\]$/)
      if (arrayMatch) {
        const [, arrayKey, index] = arrayMatch
        return current?.[arrayKey]?.[parseInt(index)]
      }

      // 处理数组筛选：projects[name=okr]
      const filterMatch = key.match(/^(\w+)\[(\w+)=(.+)\]$/)
      if (filterMatch) {
        const [, arrayKey, filterKey, filterValue] = filterMatch
        const array = current?.[arrayKey]
        if (Array.isArray(array)) {
          return array.find(item =>
            item[filterKey]?.toLowerCase().includes(filterValue.toLowerCase())
          )
        }
      }

      return current?.[key]
    }, obj)
  }

  static async processFilterExpression(expression, context) {
    // 解析筛选表达式：$filter(step1.projects, name contains "okr")
    const match = expression.match(/\$filter\(([^,]+),\s*(.+)\)/)
    if (!match) {
      throw new Error(`无效的筛选表达式：${expression}`)
    }

    const [, dataPath, condition] = match

    // 处理步骤结果引用
    if (dataPath.startsWith('step')) {
      const [stepId, ...pathParts] = dataPath.split('.')
      const stepResult = context.getStepResult(stepId)

      if (!stepResult) {
        throw new Error(`步骤结果不存在：${stepId}`)
      }

      const data = this.extractValueByPath(stepResult, pathParts.join('.'))

      if (!Array.isArray(data)) {
        throw new Error(`筛选目标必须是数组：${dataPath}`)
      }

      return this.applyFilter(data, condition)
    }

    // 其他情况使用通用解析
    const data = await this.resolveDynamicValue(`$${dataPath}`, context)

    if (!Array.isArray(data)) {
      throw new Error(`筛选目标必须是数组：${dataPath}`)
    }

    return this.applyFilter(data, condition)
  }

  static applyFilter(array, condition) {
    // 解析条件：name contains "okr"
    const conditionMatch = condition.match(/(\w+)\s+(contains|equals|startsWith)\s+"([^"]+)"/)
    if (!conditionMatch) {
      throw new Error(`无效的筛选条件：${condition}`)
    }

    const [, field, operator, value] = conditionMatch

    return array.filter(item => {
      const fieldValue = item[field]?.toString().toLowerCase() || ''
      const searchValue = value.toLowerCase()

      switch (operator) {
        case 'contains':
          return fieldValue.includes(searchValue)
        case 'equals':
          return fieldValue === searchValue
        case 'startsWith':
          return fieldValue.startsWith(searchValue)
        default:
          return false
      }
    })
  }

  static async waitForStepCompletion(stepId, context) {
    // 等待依赖步骤完成的逻辑
    return new Promise((resolve) => {
      const checkCompletion = () => {
        const stepResult = context.getStepResult(stepId)
        if (stepResult !== undefined) {
          resolve()
        } else {
          setTimeout(checkCompletion, 100)
        }
      }
      checkCompletion()
    })
  }
}
        type: 'execution_step',
        step: {
          stepId: step.stepId,
          description: step.description,
          toolName: step.toolName,
        },
        timestamp: Date.now(),
      })

      step.status = 'executing'

      try {
        // V1.1 版本：简单的工具调用（不支持动态参数）
        const validatedParams = ParameterValidator.validate(step.toolName, step.parameters)

        // 模拟工具调用（V1.1 版本先返回模拟数据）
        const result = await simulateToolCall(step.toolName, validatedParams)

        // 存储结果到上下文
        context.setStepResult(step.stepId, result)

        step.status = 'completed'

        // 推送步骤执行结果
        await sseChannel.write({
          type: 'step_result',
          stepId: step.stepId,
          result: result,
          timestamp: Date.now(),
        })
      } catch (error) {
        step.status = 'failed'
        step.error = error.message

        await sseChannel.write({
          type: 'step_error',
          stepId: step.stepId,
          error: error.message,
          timestamp: Date.now(),
        })

        throw error
      }
    }

    executionPlan.status = 'completed'

    // 推送执行完成
    await sseChannel.write({
      type: 'execution_complete',
      plan: executionPlan,
      contextData: Array.from(context.contextData.keys()),
      timestamp: Date.now(),
    })

    return executionPlan
  } catch (error) {
    executionPlan.status = 'failed'
    executionPlan.error = error.message

    await sseChannel.write({
      type: 'execution_failed',
      plan: executionPlan,
      error: error.message,
      timestamp: Date.now(),
    })

    throw error
  }
}

// V1.2 真实工具调用引擎
async function executeIntelligentPlan(executionPlan, context, sseChannel) {
  try {
    // 推送执行计划
    await sseChannel.write({
      type: 'execution_plan',
      plan: {
        planId: executionPlan.planId,
        totalSteps: executionPlan.totalSteps,
        estimatedTotalTime: executionPlan.estimatedTotalTime,
      },
      timestamp: Date.now(),
    })

    executionPlan.status = 'executing'

    for (let i = 0; i < executionPlan.steps.length; i++) {
      const step = executionPlan.steps[i]
      context.metadata.currentStep = i

      // 推送当前执行步骤
      await sseChannel.write({
        type: 'execution_step',
        step: {
          stepId: step.stepId,
          description: step.description,
          toolName: step.toolName,
          estimatedTime: step.estimatedTime,
        },
        timestamp: Date.now(),
      })

      step.status = 'executing'
      const stepStartTime = Date.now()

      try {
        // V1.2核心：动态参数解析
        const resolvedParams = await DynamicParameterResolver.resolveParameters(step, context)

        // 参数验证
        const validatedParams = ParameterValidator.validate(step.toolName, resolvedParams)

        // 真实工具调用
        const result = await callRealTool(step.toolName, validatedParams)

        // 存储结果到上下文
        context.setStepResult(step.stepId, result)

        step.status = 'completed'
        step.executionTime = Date.now() - stepStartTime

        // 推送步骤执行结果
        await sseChannel.write({
          type: 'step_result',
          stepId: step.stepId,
          result: result,
          executionTime: step.executionTime,
          contextUpdates: getContextUpdates(context),
          timestamp: Date.now(),
        })
      } catch (error) {
        step.executionTime = Date.now() - stepStartTime
        step.status = 'failed'
        step.error = error.message

        await sseChannel.write({
          type: 'step_error',
          stepId: step.stepId,
          error: error.message,
          executionTime: step.executionTime,
          timestamp: Date.now(),
        })

        // V1.2版本：简单的错误处理，V1.3版本会完善
        throw error
      }
    }

    executionPlan.status = 'completed'
    executionPlan.endTime = Date.now()
    executionPlan.totalExecutionTime = executionPlan.endTime - executionPlan.startTime

    // 推送执行完成
    await sseChannel.write({
      type: 'execution_complete',
      plan: executionPlan,
      summary: generateExecutionSummary(executionPlan, context),
      timestamp: Date.now(),
    })

    return executionPlan
  } catch (error) {
    executionPlan.status = 'failed'
    executionPlan.error = error.message

    await sseChannel.write({
      type: 'execution_failed',
      plan: executionPlan,
      error: error.message,
      timestamp: Date.now(),
    })

    throw error
  }
}

// 真实工具调用函数
async function callRealTool(toolName, parameters) {
  const toolConfig = TOOL_REGISTRY[toolName]
  if (!toolConfig) {
    throw new Error(`未找到工具：${toolName}`)
  }

  try {
    // 调用对应的云函数
    const cloudFunction = uniCloud.importObject(toolConfig.cloudFunction)
    const result = await cloudFunction[toolConfig.method](parameters)

    return result
  } catch (error) {
    throw new Error(`工具执行失败：${error.message}`)
  }
}

// 获取上下文更新信息
function getContextUpdates(context) {
  const updates = {}
  for (const [key, value] of context.contextData.entries()) {
    updates[key] = value
  }
  return updates
}

// 生成执行摘要
function generateExecutionSummary(executionPlan, context) {
  const completedSteps = executionPlan.steps.filter((s) => s.status === 'completed')
  const failedSteps = executionPlan.steps.filter((s) => s.status === 'failed')

  return {
    totalSteps: executionPlan.totalSteps,
    completedSteps: completedSteps.length,
    failedSteps: failedSteps.length,
    totalExecutionTime: executionPlan.totalExecutionTime,
    averageStepTime:
      completedSteps.length > 0
        ? Math.round(completedSteps.reduce((sum, s) => sum + s.executionTime, 0) / completedSteps.length)
        : 0,
    contextDataKeys: Array.from(context.contextData.keys()),
    success: failedSteps.length === 0,
  }
}

// 模拟工具调用（V1.1 版本使用，V1.2 版本替换为真实调用）
async function simulateToolCall(toolName, parameters) {
  // 模拟延迟
  await new Promise((resolve) => setTimeout(resolve, 1000))

  switch (toolName) {
    case 'getProjects':
      return {
        success: true,
        data: [
          { id: 'proj-1', name: 'OKR 项目', description: '目标管理项目' },
          { id: 'proj-2', name: '日常任务', description: '日常工作任务' },
        ],
        metadata: { total: 2, filtered: parameters.filter ? 1 : 2 },
      }

    case 'getTasks':
      return {
        success: true,
        tasks: [
          { id: 'task-1', title: '制定 Q1 目标', completed: false, projectId: parameters.projectId || 'proj-1' },
          { id: 'task-2', title: '更新 KR 进度', completed: false, projectId: parameters.projectId || 'proj-1' },
        ],
        metadata: { total: 2, projectId: parameters.projectId },
      }

    default:
      throw new Error(`未知的工具：${toolName}`)
  }
}

// V1.1 版本新增的 SSE 消息类型
const SSE_MESSAGE_TYPES = {
  // 现有类型保持不变
  start: '开始生成回复',
  intent_type: '意图类型识别',
  intent_content_start: '意图内容开始',
  intent_content_chunk: '意图内容块',
  end: '结束',
  error: '错误',

  // V1.1 新增类型
  execution_plan_start: '执行计划开始',
  execution_step: '执行步骤',
  step_result: '步骤结果',
  step_error: '步骤错误',
  execution_complete: '执行完成',
  execution_failed: '执行失败',
}

module.exports = {
  _before: function () {
    // 通用预处理器
  },

  /**
   * 与 DeepSeek AI 进行聊天对话
   * @param {string} message - 用户当前发送的消息内容
   * @param {Array} messages - 历史对话消息数组，每条消息包含 role 和 content
   * @param {string} model - AI 模型名称，默认为'deepseek-chat'
   * @param {string} system - 系统提示词，用于设定 AI 助手的行为和角色
   * @returns {object} AI 回复内容或错误信息
   */
  async speak() {
    // 获取 HTTP 请求信息
    const httpInfo = this.getHttpInfo()
    // 解析请求体，提取参数
    let {
      message,
      history_records = [], // 默认为空数组
      model = 'deepseek-chat', // 默认使用 deepseek-chat 模型
      system = 'You are a helpful assistant.', // 默认系统提示词
    } = JSON.parse(httpInfo.body)

    // 参数校验：确保 message 不为空
    if (!message) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '消息内容不能为空',
      }
    }

    try {
      // 初始化 OpenAI 客户端，配置为 DeepSeek API
      const openai = new OpenAI({
        baseURL: 'https://api.deepseek.com/v1', // DeepSeek API 地址
        apiKey: '***********************************', // API 密钥
      })

      // 调用聊天完成接口
      const messages = [{ role: 'system', content: system }, ...history_records]
      if (message) messages.push({ role: 'user', content: message })
      const completion = await openai.chat.completions.create({
        // 构建消息数组：系统提示词 + 历史消息 + 当前用户消息
        messages,
        model: model, // 使用指定的模型
      })

      // 返回 AI 回复的内容
      return {
        content: completion.choices[0].message.content,
      }
    } catch (error) {
      // 错误处理：返回错误代码和消息
      return {
        errCode: 'API_ERROR',
        errMsg: error.message || '调用 AI 接口失败',
      }
    }
  },
  // async chat(params) {
  //   let {
  //     message,
  //     messages: history_records = [], // 默认为空数组
  //     model = 'doubao-seed-1-6-250615', // 默认使用 doubao 模型
  //     system = 'You are a helpful assistant.', // 默认系统提示词
  //   } = params

  //   console.log('message', message)

  //   // 参数校验：确保 message 不为空
  //   if (!message) {
  //     return {
  //       errCode: 'PARAM_IS_NULL',
  //       errMsg: '消息内容不能为空',
  //     }
  //   }
  //   console.log('发送消息')

  //   try {
  // console.time('初始化')
  //     // 初始化 OpenAI 客户端
  //     const openai = new OpenAI(doubaoParams)
  // console.timeEnd('初始化')
  //     // 调用聊天完成接口
  //     const messages = [{ role: 'system', content: system }, ...history_records]
  //     if (message) messages.push({ role: 'user', content: message })
  // console.time('获取响应流')
  //     // 获取响应流
  //     const streamResponse = await openai.chat.completions.create({
  //       messages,
  //       model: model,
  //       stream: true, // 始终开启流式响应
  //     })
  //  console.timeEnd('获取响应流')

  //     // 处理流式响应，收集所有数据块
  //     let fullContent = ''
  //     const chunks = []

  //     for await (const chunk of streamResponse) {
  //       const content = chunk.choices[0]?.delta?.content || ''
  //       console.log(content)
  //       if (content) {
  //         fullContent += content
  //         chunks.push({
  //           content: content,
  //           timestamp: Date.now(),
  //         })
  //       }
  //     }

  //     // 返回流式响应的完整结果
  //     return {
  //       errCode: 0,
  //       errMsg: 'success',
  //       data: {
  //         type: 'stream',
  //         content: fullContent, // 完整内容
  //         chunks: chunks, // 分块数据，可用于前端逐步显示
  //       },
  //     }
  //   } catch (error) {
  //     // 错误处理：返回标准失败响应结构
  //     console.log(error)
  //     return {
  //       errCode: 'API_ERROR',
  //       errMsg: error.message || '调用 AI 接口失败',
  //     }
  //   }
  // },

  /**
   * 基于 SSE Channel 的流式聊天接口 - 实时推送 AI 响应
   * @param {object} params - 参数对象
   * @returns {object} 流式响应结果
   */
  async chatStreamSSE(params) {
    let {
      message,
      messages: history_records = [],
      model = 'doubao-seed-1-6-250615',
      system = `你是一个专业的任务分析助手。你的主要职责是理解用户输入的内容，并判断用户的意图类型。

请分析用户输入内容，并将其归类为以下三种意图之一：
1. create_task: 当用户想要创建、添加、设置、安排一个新任务时
2. find_task: 当用户想要查询、搜索、查看已有的任务时
3. chat: 其他所有不属于创建任务或查找任务的内容，视为一般闲聊

分析完成后，必须严格按照以下格式输出结果：
「意图类型」：[意图类型代码，必须是 create_task、find_task 或 chat 之一]
「意图内容」：[如果是创建任务，提取出要创建的任务内容；如果是查找任务，提取出要查找的任务关键词；如果是闲聊，则回复用户问题]

注意：
- 分析要准确，不要混淆不同意图类型
- 只输出上述指定的两行内容，不要添加任何其他解释或内容
- 确保格式严格遵循示例，包括使用中文引号「」`,
      channel, // SSE Channel 对象
    } = params

    console.log('SSE 流式聊天消息：', message)

    // 参数校验
    if (!message) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: '消息内容不能为空',
      }
    }

    if (!channel) {
      return {
        errCode: 'PARAM_IS_NULL',
        errMsg: 'SSE Channel 不能为空',
      }
    }

    try {
      // 反序列化 SSE Channel
      const sseChannel = uniCloud.deserializeSSEChannel(channel)

      // 初始化 OpenAI 客户端
      const openai = new OpenAI(doubaoParams)
      // 构建消息数组
      const messages = [{ role: 'system', content: system }, ...history_records]
      if (message) messages.push({ role: 'user', content: message })

      // 发送开始消息
      await sseChannel.write({
        type: 'start',
        message: '开始生成回复...',
        timestamp: Date.now(),
      })

      // 创建流式响应，设置 5 分钟超时
      const streamResponse = await openai.chat.completions.create({
        messages,
        model: model,
        stream: true,
        timeout: 300000, // 5 分钟超时（毫秒）
      })

      // 处理流式数据并实时推送
      let fullContent = ''
      let chunkCount = 0

      // 意图识别相关变量
      let intentType = null
      let isIntentContentStarted = false
      let intentContent = ''

      // 意图类型和内容的正则表达式
      const intentTypeRegex = /「意图类型」：(create_task|find_task|chat)/
      const intentContentRegex = /「意图内容」：([\s\S]*)/

      for await (const chunk of streamResponse) {
        const content = chunk.choices[0]?.delta?.content || ''
        if (content) {
          fullContent += content
          chunkCount++

          // 检测意图类型
          if (!intentType) {
            const typeMatch = intentTypeRegex.exec(fullContent)
            if (typeMatch) {
              intentType = typeMatch[1]
              console.log(`检测到意图类型：${intentType}`)
              // 推送意图类型
              await sseChannel.write({
                type: 'intent_type',
                intentType: intentType,
                timestamp: Date.now(),
              })
              continue // 跳过当前块的推送
            }
          }

          // 检测意图内容
          if (intentType && !isIntentContentStarted) {
            const contentMatch = intentContentRegex.exec(fullContent)
            if (contentMatch) {
              isIntentContentStarted = true
              intentContent = contentMatch[1]
              console.log('检测到意图内容开始')
              // 推送意图内容开始
              await sseChannel.write({
                type: 'intent_content_start',
                content: intentContent,
                timestamp: Date.now(),
              })
              continue // 跳过当前块的推送
            }
          } else if (isIntentContentStarted) {
            // 继续推送意图内容的后续部分
            await sseChannel.write({
              type: 'intent_content_chunk',
              content: content,
              timestamp: Date.now(),
            })
            intentContent += content
            console.log(`推送意图内容块：${content}`)
          } else {
            // 尚未检测到任何模式，继续累积内容
            console.log(`累积内容：${fullContent}`)
          }
        }
      }

      // 打印完整的 AI 返回内容，用于调试
      console.log('AI 完整返回内容：', fullContent)
      console.log('提取的意图类型：', intentType)
      console.log('提取的意图内容：', intentContent)

      // V1.2 升级：在意图识别完成后执行任务
      if (intentType && intentType !== 'chat') {
        // 创建执行上下文
        const context = new ExecutionContextManager(IntelligentExecutionPlanner.generateUUID(), message)

        // 使用智能执行计划生成器
        const executionPlan = await IntelligentExecutionPlanner.generatePlan(message, intentType)

        if (executionPlan.totalSteps > 0) {
          // 使用智能执行引擎
          await executeIntelligentPlan(executionPlan, context, sseChannel)

          // 修改最终返回结果
          return {
            errCode: 0,
            errMsg: 'success',
            data: {
              type: 'task_executed',
              intentType: intentType,
              executionPlan: executionPlan,
              contextData: Array.from(context.contextData.keys()),
              executionTime: executionPlan.totalExecutionTime,
              content: isIntentContentStarted ? intentContent : fullContent,
              totalChunks: chunkCount,
            },
          }
        }
      }

      // 发送结束消息
      await sseChannel.end({
        type: 'end',
        content: isIntentContentStarted ? intentContent : fullContent,
        intentType: intentType,
        totalChunks: chunkCount,
        timestamp: Date.now(),
      })

      console.log(`SSE 流式聊天完成，共推送${chunkCount}个数据块`)

      return {
        errCode: 0,
        errMsg: 'success',
        data: {
          type: 'stream_complete',
          content: isIntentContentStarted ? intentContent : fullContent,
          intentType: intentType,
          totalChunks: chunkCount,
        },
      }
    } catch (error) {
      console.log('SSE 流式聊天错误：', error)

      // 如果有 channel，尝试发送错误消息
      try {
        if (channel) {
          const sseChannel = uniCloud.deserializeSSEChannel(channel)
          await sseChannel.end({
            type: 'error',
            error: error.message || '调用 AI 流式接口失败',
            timestamp: Date.now(),
          })
        }
      } catch (channelError) {
        console.log('发送错误消息失败：', channelError)
      }

      return {
        errCode: 'API_ERROR',
        errMsg: error.message || '调用 AI 流式接口失败',
      }
    }
  },
}
